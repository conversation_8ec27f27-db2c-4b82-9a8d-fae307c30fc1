#!/usr/bin/env python3
"""
Test AI Coding Agent database configuration.
"""

def test_ai_coding_agent_config():
    """Test the AI Coding Agent database configuration."""
    print("🤖 AI Coding Agent Database Configuration Test")
    print("=" * 55)

    try:
        from src.ai_coding_agent.config import settings

        print("✅ Configuration loaded successfully")
        print()

        # Database mode
        print(f"📊 Database Mode: {settings.hybrid_db.mode}")

        # Local tables (fast operations)
        local_tables = settings.hybrid_db.local_table_list
        print(f"🏠 Local Tables: {len(local_tables)} tables")
        for table in local_tables:
            print(f"   • {table}")

        # Cloud tables (shared knowledge)
        cloud_tables = settings.hybrid_db.supabase_table_list
        print(f"☁️ Cloud Tables: {len(cloud_tables)} tables")
        for table in cloud_tables:
            print(f"   • {table}")

        # Supabase configuration
        supabase_configured = "Yes" if settings.supabase.url else "No"
        print(f"🔗 Supabase URL configured: {supabase_configured}")

        if settings.supabase.url:
            print(f"   URL: {settings.supabase.url[:50]}...")

        # Vector database
        print(f"🧠 Vector DB Directory: {getattr(settings.hybrid_db, 'vector_directory', './vector_db')}")

        print()
        print("✅ AI Coding Agent database configuration is ready!")

        return True

    except Exception as e:
        print(f"❌ Configuration error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ai_coding_agent_config()
