#!/usr/bin/env python3
"""
Test script to verify JSON serialization integration works correctly.
Run this to confirm everything is working in your AI Coding Agent project.
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
from uuid import uuid4
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_serialization():
    """Test 1: Basic serialization functionality"""
    print("🧪 Test 1: Basic Serialization")
    
    try:
        from ai_coding_agent.utils.serialization import serialize_for_json, to_json
        
        # Test complex data
        test_data = {
            "timestamp": datetime.now(),
            "task_id": uuid4(),
            "price": Decimal("99.99"),
            "binary": b"test data",
            "nested": {
                "date": datetime.now().date(),
                "uuid": uuid4(),
                "set": {1, 2, 3}
            }
        }
        
        # This should work without errors
        serialized = serialize_for_json(test_data)
        json_string = to_json(test_data)
        
        print("✅ Basic serialization works!")
        print(f"   JSON length: {len(json_string)} characters")
        return True
        
    except Exception as e:
        print(f"❌ Basic serialization failed: {e}")
        return False


def test_audit_service_integration():
    """Test 2: Audit service integration"""
    print("\n🧪 Test 2: Audit Service Integration")
    
    try:
        from ai_coding_agent.services.audit import AuditService, setup_audit_serialization_hooks
        from ai_coding_agent.utils.serialization import serialize_for_json
        
        # Test that audit service can handle complex data
        complex_audit_data = {
            "old_values": {
                "status": "pending",
                "created_at": datetime.now(),
                "metadata": {
                    "priority": "high",
                    "cost": Decimal("150.00"),
                    "id": uuid4()
                }
            },
            "new_values": {
                "status": "completed",
                "updated_at": datetime.now(),
                "metadata": {
                    "priority": "high", 
                    "cost": Decimal("175.50"),
                    "id": uuid4(),
                    "completion_time": timedelta(hours=3)
                }
            }
        }
        
        # Test serialization
        serialized = serialize_for_json(complex_audit_data)
        
        print("✅ Audit service integration works!")
        print(f"   Serialized audit data successfully")
        return True
        
    except Exception as e:
        print(f"❌ Audit service integration failed: {e}")
        return False


def test_dependency_engine_integration():
    """Test 3: Dependency engine integration"""
    print("\n🧪 Test 3: Dependency Engine Integration")
    
    try:
        from ai_coding_agent.utils.serialization import serialize_dependency_data
        
        # Simulate complex dependency data
        dependency_data = {
            "check_id": str(uuid4()),
            "timestamp": datetime.now(),
            "blocking_dependencies": [
                {
                    "id": str(uuid4()),
                    "type": "TASK",
                    "estimated_completion": datetime.now() + timedelta(hours=2),
                    "metadata": {
                        "priority": Decimal("0.8"),
                        "cost": Decimal("45.50")
                    }
                }
            ],
            "cache_stats": {
                "hit_rate": Decimal("0.85"),
                "last_updated": datetime.now()
            }
        }
        
        # Test dependency-specific serialization
        serialized = serialize_dependency_data(dependency_data)
        
        print("✅ Dependency engine integration works!")
        print(f"   Dependency data serialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Dependency engine integration failed: {e}")
        return False


def test_error_handling():
    """Test 4: Error handling with weird objects"""
    print("\n🧪 Test 4: Error Handling")
    
    try:
        from ai_coding_agent.utils.serialization import serialize_for_json
        
        # Create problematic objects
        class WeirdClass:
            def __init__(self):
                self.data = datetime.now()
                self.uuid = uuid4()
        
        class UnserializableClass:
            def __str__(self):
                raise Exception("Cannot stringify!")
        
        weird_data = {
            "normal": "string",
            "weird_object": WeirdClass(),
            "unserializable": UnserializableClass(),
            "nested_weird": {
                "another_weird": WeirdClass(),
                "set_with_datetime": {datetime.now(), "string", 123}
            }
        }
        
        # This should handle everything gracefully
        serialized = serialize_for_json(weird_data)
        
        print("✅ Error handling works!")
        print(f"   Even weird objects handled gracefully")
        return True
        
    except Exception as e:
        print(f"❌ Error handling failed: {e}")
        return False


def test_performance():
    """Test 5: Performance with large objects"""
    print("\n🧪 Test 5: Performance Test")
    
    try:
        from ai_coding_agent.utils.serialization import serialize_for_json
        import time
        
        # Create large complex object
        large_data = {
            "tasks": [
                {
                    "id": str(uuid4()),
                    "created_at": datetime.now(),
                    "metadata": {
                        "cost": Decimal(f"{i * 10.50}"),
                        "priority": Decimal(f"0.{i % 10}"),
                        "tags": {f"tag_{j}" for j in range(5)}
                    }
                }
                for i in range(100)  # 100 complex tasks
            ],
            "timestamp": datetime.now(),
            "summary": {
                "total_cost": Decimal("10500.00"),
                "avg_priority": Decimal("0.45")
            }
        }
        
        # Time the serialization
        start_time = time.time()
        serialized = serialize_for_json(large_data)
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"✅ Performance test passed!")
        print(f"   Serialized 100 complex objects in {duration:.3f} seconds")
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False


def main():
    """Run all integration tests"""
    print("🚀 AI Coding Agent - JSON Serialization Integration Tests")
    print("=" * 60)
    
    tests = [
        test_basic_serialization,
        test_audit_service_integration,
        test_dependency_engine_integration,
        test_error_handling,
        test_performance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! JSON serialization is working perfectly!")
        print("\n💡 What this means:")
        print("   ✅ Your audit trails will never break")
        print("   ✅ Dependency engine logging is bulletproof")
        print("   ✅ AI responses can be safely stored")
        print("   ✅ Complex objects are handled gracefully")
        print("   ✅ Performance is good even with large data")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the errors above.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
