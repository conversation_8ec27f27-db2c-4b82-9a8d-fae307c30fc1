"""
Admin router for model configuration and system management.

Provides endpoints for:
- Model configuration management (local vs cloud)
- Available model detection
- Agent role assignment
- System health monitoring

SECURITY: All endpoints require admin authentication and are audited.
"""

from typing import Dict, List, Optional, Any
import json
import httpx
from pathlib import Path

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field, validator

from ..config import get_settings
from ..orchestrator import OrchestratorConfig
from ..middleware.admin_auth import get_current_admin_user
from ..services.secure_config import secure_config
from ..services.admin_audit import admin_audit_logger, AuditAction, AuditSeverity
from ..models import User

router = APIRouter(prefix="/api/v1/admin", tags=["admin"])


@router.get("/auth/check")
async def check_admin_auth(
    current_admin: User = Depends(get_current_admin_user)
):
    """
    Check if current user has admin privileges.

    This endpoint is used by the frontend to verify admin access.
    """
    return {
        "is_superuser": getattr(current_admin, 'is_superuser', False),
        "permissions": ["admin"],  # In future, this could be more granular
        "user_id": current_admin.id,
        "username": current_admin.username
    }

class ModelProvider(BaseModel):
    """Model provider configuration."""
    name: str
    type: str  # "local" or "cloud"
    host: Optional[str] = None
    api_key: Optional[str] = None
    models: List[str] = []
    status: str = "unknown"  # "online", "offline", "error"

class AgentModelConfig(BaseModel):
    """Agent model assignment configuration."""
    agent_name: str
    primary_model: str
    secondary_model: Optional[str] = None
    fallback_model: Optional[str] = None
    provider: str  # "ollama", "openai", "anthropic", etc.

class ModelConfigUpdate(BaseModel):
    """Model configuration update request."""
    agent_configs: List[AgentModelConfig]
    provider_configs: Dict[str, Dict[str, Any]]

class CloudProviderConfig(BaseModel):
    """Cloud provider configuration with validation."""
    provider_name: str = Field(..., regex="^(openai|anthropic|google)$")
    api_key: str = Field(..., min_length=10, max_length=200)
    base_url: Optional[str] = Field(None, regex="^https?://.*")
    enabled: bool = True

    @validator('api_key')
    def validate_api_key_format(cls, v, values):
        """Validate API key format based on provider."""
        provider = values.get('provider_name')

        if provider == 'openai' and not v.startswith('sk-'):
            raise ValueError('OpenAI API keys must start with "sk-"')
        elif provider == 'anthropic' and not v.startswith('sk-ant-'):
            raise ValueError('Anthropic API keys must start with "sk-ant-"')
        elif provider == 'google' and len(v) < 20:
            raise ValueError('Google API keys must be at least 20 characters')

        return v

@router.get("/models/available", response_model=Dict[str, ModelProvider])
async def get_available_models(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get all available models from local and cloud providers."""
    providers = {}

    # Check Ollama (local models)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
            if response.status_code == 200:
                ollama_data = response.json()
                ollama_models = [model["name"] for model in ollama_data.get("models", [])]
                providers["ollama"] = ModelProvider(
                    name="Ollama",
                    type="local",
                    host="http://localhost:11434",
                    models=ollama_models,
                    status="online" if ollama_models else "offline"
                )
            else:
                providers["ollama"] = ModelProvider(
                    name="Ollama",
                    type="local",
                    host="http://localhost:11434",
                    models=[],
                    status="offline"
                )
    except Exception:
        providers["ollama"] = ModelProvider(
            name="Ollama",
            type="local",
            host="http://localhost:11434",
            models=[],
            status="offline"
        )

    # Add cloud providers (with placeholder models)
    providers["openai"] = ModelProvider(
        name="OpenAI",
        type="cloud",
        models=["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"],
        status="unknown"  # Will be checked when API key is provided
    )

    providers["anthropic"] = ModelProvider(
        name="Anthropic",
        type="cloud",
        models=["claude-3-opus", "claude-3-sonnet", "claude-3-haiku", "claude-2.1"],
        status="unknown"
    )

    providers["google"] = ModelProvider(
        name="Google",
        type="cloud",
        models=["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro"],
        status="unknown"
    )

    return providers

@router.get("/models/current-config")
async def get_current_model_config(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get current model configuration for all agents."""
    config_path = Path(__file__).parent.parent / "models_config.json"

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Extract current agent configurations
        routing = config.get("routing", {})
        agent_configs = []

        for agent_name, agent_config in routing.items():
            agent_configs.append(AgentModelConfig(
                agent_name=agent_name,
                primary_model=agent_config.get("primary", ""),
                secondary_model=agent_config.get("secondary"),
                fallback_model=agent_config.get("fallback"),
                provider="ollama"  # Default for now
            ))

        return {
            "agent_configs": agent_configs,
            "providers": config.get("providers", {}),
            "last_updated": "2024-01-01T00:00:00Z"  # TODO: Add timestamp tracking
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load configuration: {str(e)}")

@router.post("/models/update-config")
async def update_model_config(
    config_update: ModelConfigUpdate,
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Update model configuration for agents."""
    config_path = Path(__file__).parent.parent / "models_config.json"

    try:
        # Load current configuration for audit logging
        old_config = secure_config.load_config()

        # Load current configuration
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Update routing configuration
        routing = config.get("routing", {})

        for agent_config in config_update.agent_configs:
            routing[agent_config.agent_name] = {
                "primary": agent_config.primary_model,
                "secondary": agent_config.secondary_model,
                "fallback": agent_config.fallback_model or agent_config.primary_model,
                "task_routing": routing.get(agent_config.agent_name, {}).get("task_routing", {})
            }

        config["routing"] = routing

        # Update provider configurations
        if config_update.provider_configs:
            config["providers"].update(config_update.provider_configs)

        # Save updated configuration
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)

        # Audit log the configuration change
        admin_audit_logger.log_model_config_change(
            user=current_admin,
            old_config=old_config,
            new_config=config,
            ip_address=request.client.host if request.client else None
        )

        return {"success": True, "message": "Configuration updated successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update configuration: {str(e)}")

@router.post("/providers/cloud/configure")
async def configure_cloud_provider(
    provider_config: CloudProviderConfig,
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Configure a cloud provider with API key (securely encrypted)."""
    try:
        # Use secure config manager to encrypt and store API key
        success = secure_config.save_provider_config(
            provider_name=provider_config.provider_name,
            api_key=provider_config.api_key,
            base_url=provider_config.base_url,
            enabled=provider_config.enabled
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to save provider configuration")

        # Audit log the provider configuration (without exposing API key)
        masked_details = {
            "provider_name": provider_config.provider_name,
            "base_url": provider_config.base_url,
            "enabled": provider_config.enabled,
            "api_key_length": len(provider_config.api_key),
            "api_key_prefix": provider_config.api_key[:8] + "..." if len(provider_config.api_key) > 8 else "***"
        }

        admin_audit_logger.log_provider_config_change(
            user=current_admin,
            provider_name=provider_config.provider_name,
            action_type="add",
            masked_details=masked_details,
            ip_address=request.client.host if request.client else None
        )

        return {"success": True, "message": f"{provider_config.provider_name} configured successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to configure provider: {str(e)}")

@router.get("/system/health")
async def get_system_health(
    request: Request,
    current_admin: User = Depends(get_current_admin_user)
):
    """Get overall system health including model availability."""
    health_status = {
        "ollama": {"status": "unknown", "models_count": 0},
        "cloud_providers": {},
        "agents": {},
        "overall_status": "unknown"
    }

    # Check Ollama
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
            if response.status_code == 200:
                models = response.json().get("models", [])
                health_status["ollama"] = {
                    "status": "online",
                    "models_count": len(models),
                    "models": [m["name"] for m in models]
                }
            else:
                health_status["ollama"]["status"] = "offline"
    except Exception:
        health_status["ollama"]["status"] = "offline"

    # Check agent configurations
    try:
        config = OrchestratorConfig()
        routing = config.get_routing_config()

        for agent_name, agent_config in routing.items():
            primary_model = agent_config.get("primary")
            health_status["agents"][agent_name] = {
                "primary_model": primary_model,
                "configured": bool(primary_model),
                "provider": "ollama"  # Default assumption
            }
    except Exception as e:
        health_status["agents"] = {"error": str(e)}

    # Determine overall status
    ollama_ok = health_status["ollama"]["status"] == "online"
    agents_configured = len(health_status["agents"]) > 0

    if ollama_ok and agents_configured:
        health_status["overall_status"] = "healthy"
    elif agents_configured:
        health_status["overall_status"] = "degraded"
    else:
        health_status["overall_status"] = "unhealthy"

    return health_status

@router.post("/models/test-connection")
async def test_model_connection(
    model_name: str,
    provider: str = "ollama",
    request: Request = None,
    current_admin: User = Depends(get_current_admin_user)
):
    """Test connection to a specific model."""
    if provider == "ollama":
        try:
            async with httpx.AsyncClient() as client:
                # Test if model is available
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json={"model": model_name, "prompt": "test", "stream": False},
                    timeout=10.0
                )

                if response.status_code == 200:
                    return {"success": True, "message": f"Model {model_name} is available"}
                else:
                    return {"success": False, "message": f"Model {model_name} not available"}
        except Exception as e:
            return {"success": False, "message": f"Connection failed: {str(e)}"}

    else:
        # TODO: Implement cloud provider testing
        return {"success": False, "message": f"Testing for {provider} not implemented yet"}
