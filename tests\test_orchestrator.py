"""
Tests for Enhanced Orchestrator - Phase A2 Implementation

This module contains comprehensive tests for the enhanced orchestrator functionality
including model routing, load balancing, health monitoring, and analytics.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from typing import Dict, Any

from ai_coding_agent.orchestrator import (
    EnhancedOrchestrator,
    TaskContext,
    TaskType,
    TaskComplexity,
    ModelPerformanceMetrics,
    LoadBalancingState,
    ModelHealthMonitor
)


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        "providers": {
            "ollama": {
                "models": {
                    "test-model-1": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": "Test model 1"
                    },
                    "test-model-2": {
                        "role": "test",
                        "agents": ["test_agent"],
                        "description": "Test model 2"
                    }
                }
            }
        },
        "routing": {
            "test_agent": {
                "primary": "test-model-1",
                "secondary": "test-model-2",
                "fallback": "test-model-1"
            }
        },
        "load_balancing": {
            "strategy": "round_robin",
            "health_weight": 0.4,
            "performance_weight": 0.3,
            "availability_weight": 0.3
        },
        "model_analytics": {
            "track_performance": True
        }
    }


@pytest.fixture
def mock_orchestrator(mock_config):
    """Create a mock orchestrator for testing."""
    with patch('ai_coding_agent.orchestrator.OrchestratorConfig') as mock_config_class:
        mock_config_instance = Mock()
        mock_config_instance.config = mock_config
        mock_config_instance.get_routing_config.return_value = mock_config["routing"]
        mock_config_instance.get_performance_settings.return_value = {}
        mock_config_instance.get_quality_thresholds.return_value = {}
        mock_config_class.return_value = mock_config_instance

        orchestrator = EnhancedOrchestrator()
        orchestrator.client = AsyncMock()
        return orchestrator


class TestModelRouting:
    """Test model routing functionality."""

    @pytest.mark.asyncio
    async def test_basic_model_routing(self, mock_orchestrator):
        """Test basic model routing functionality."""
        model = await mock_orchestrator.route_model_by_task(
            "test_agent", TaskType.CODE_GENERATION, TaskComplexity.SIMPLE
        )
        assert model == "test-model-1"

    @pytest.mark.asyncio
    async def test_complexity_based_routing(self, mock_orchestrator):
        """Test that complex tasks route to secondary models."""
        model = await mock_orchestrator.route_model_by_task(
            "test_agent", TaskType.CODE_GENERATION, TaskComplexity.EXPERT
        )
        # Should route to secondary for complex tasks
        assert model in ["test-model-2", "test-model-1"]

    @pytest.mark.asyncio
    async def test_unknown_agent_fallback(self, mock_orchestrator):
        """Test fallback behavior for unknown agents."""
        with patch('ai_coding_agent.config.settings') as mock_settings:
            mock_settings.ai.default_model = "default-model"

            model = await mock_orchestrator.route_model_by_task(
                "unknown_agent", TaskType.CODE_GENERATION, TaskComplexity.SIMPLE
            )
            assert model == "default-model"


class TestTaskComplexityAssessment:
    """Test task complexity assessment functionality."""

    @pytest.mark.asyncio
    async def test_simple_task_assessment(self, mock_orchestrator):
        """Test assessment of simple tasks."""
        complexity = await mock_orchestrator._assess_task_complexity(
            "Fix typo", None
        )
        assert complexity == TaskComplexity.SIMPLE

    @pytest.mark.asyncio
    async def test_complex_task_assessment(self, mock_orchestrator):
        """Test assessment of complex tasks."""
        task = """
        Design a comprehensive microservices architecture with authentication,
        authorization, database design, performance optimization, and scalability
        considerations for a large-scale enterprise application.
        """
        complexity = await mock_orchestrator._assess_task_complexity(
            task, TaskType.ARCHITECTURE
        )
        assert complexity in [TaskComplexity.COMPLEX, TaskComplexity.EXPERT]

    @pytest.mark.asyncio
    async def test_task_type_classification(self, mock_orchestrator):
        """Test task type classification."""
        test_cases = [
            ("Create a React component", TaskType.UI_COMPONENT),
            ("Debug this error", TaskType.DEBUGGING),
            ("Design an API", TaskType.API_DESIGN),
            ("Plan the project", TaskType.PLANNING)
        ]

        for task, expected_type in test_cases:
            result = await mock_orchestrator._classify_task_type(task)
            assert result == expected_type


class TestQualityAssessment:
    """Test quality assessment functionality."""

    @pytest.mark.asyncio
    async def test_confidence_assessment(self, mock_orchestrator):
        """Test confidence scoring."""
        high_confidence_response = """
        Here's a complete implementation:

        ```python
        def function():
            return "result"
        ```

        This approach works because it implements the required functionality.
        """

        low_confidence_response = "Maybe try this, but I'm not sure if it will work."

        high_score = await mock_orchestrator._assess_confidence(
            high_confidence_response, TaskType.CODE_GENERATION
        )
        low_score = await mock_orchestrator._assess_confidence(
            low_confidence_response, TaskType.CODE_GENERATION
        )

        assert high_score > low_score
        assert high_score > 0.7
        assert low_score < 0.6

    @pytest.mark.asyncio
    async def test_artifact_extraction(self, mock_orchestrator):
        """Test artifact extraction from responses."""
        response_with_code = """
        Here's the solution:

        ```python
        def hello():
            print("Hello World")
        ```

        Save this to: main.py
        """

        artifacts = await mock_orchestrator._extract_artifacts(response_with_code)

        assert len(artifacts) >= 1
        assert any(artifact["type"] == "code" for artifact in artifacts)
        assert any(artifact["language"] == "python" for artifact in artifacts)


class TestLoadBalancing:
    """Test load balancing functionality."""

    def test_load_balancing_state(self):
        """Test load balancing state management."""
        state = LoadBalancingState()

        # Test request recording
        state.record_request("model-1")
        assert state.current_loads["model-1"] == 1
        assert state.request_counts["model-1"] == 1

        # Test request release
        state.release_request("model-1")
        assert state.current_loads["model-1"] == 0

    def test_least_loaded_selection(self):
        """Test least loaded model selection."""
        state = LoadBalancingState()

        # Set up different loads
        state.current_loads = {"model-1": 3, "model-2": 1, "model-3": 2}

        least_loaded = state.get_least_loaded_model(["model-1", "model-2", "model-3"])
        assert least_loaded == "model-2"

    def test_round_robin_selection(self):
        """Test round robin model selection."""
        state = LoadBalancingState()
        models = ["model-1", "model-2", "model-3"]

        # Test round robin cycling
        selections = []
        for _ in range(6):
            selections.append(state.get_round_robin_model(models))

        # Should cycle through models
        assert selections == ["model-1", "model-2", "model-3", "model-1", "model-2", "model-3"]


class TestPerformanceMetrics:
    """Test performance metrics tracking."""

    def test_metrics_initialization(self):
        """Test metrics initialization."""
        metrics = ModelPerformanceMetrics("test-model")

        assert metrics.model_name == "test-model"
        assert metrics.error_count == 0
        assert metrics.success_count == 0
        assert metrics.health_score == 1.0

    def test_performance_data_recording(self):
        """Test recording performance data."""
        metrics = ModelPerformanceMetrics("test-model")

        # Record successful operation
        metrics.add_performance_data(1.5, 0.8, True)

        assert metrics.success_count == 1
        assert metrics.error_count == 0
        assert len(metrics.response_times) == 1
        assert len(metrics.quality_scores) == 1
        assert metrics.consecutive_failures == 0

    def test_health_score_calculation(self):
        """Test health score calculation."""
        metrics = ModelPerformanceMetrics("test-model")

        # Add good performance data
        for _ in range(5):
            metrics.add_performance_data(1.0, 0.9, True)

        assert metrics.health_score > 0.8

        # Add failure data
        for _ in range(3):
            metrics.add_performance_data(10.0, 0.1, False)

        assert metrics.health_score < 0.8


class TestHealthMonitoring:
    """Test health monitoring functionality."""

    @pytest.mark.asyncio
    async def test_health_check_success(self):
        """Test successful health check."""
        mock_client = AsyncMock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_client.post.return_value = mock_response

        monitor = ModelHealthMonitor(mock_client)

        is_healthy = await monitor.check_model_health("test-model")
        assert is_healthy is True
        assert monitor.model_status["test-model"] is True

    @pytest.mark.asyncio
    async def test_health_check_failure(self):
        """Test failed health check."""
        mock_client = AsyncMock()
        mock_response = Mock()
        mock_response.status_code = 500
        mock_client.post.return_value = mock_response

        monitor = ModelHealthMonitor(mock_client)

        is_healthy = await monitor.check_model_health("test-model")
        assert is_healthy is False
        assert monitor.model_status["test-model"] is False

    @pytest.mark.asyncio
    async def test_health_check_caching(self):
        """Test health check result caching."""
        mock_client = AsyncMock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_client.post.return_value = mock_response

        monitor = ModelHealthMonitor(mock_client)

        # First check
        await monitor.check_model_health("test-model")

        # Second check should use cache
        await monitor.check_model_health("test-model")

        # Should only have called once due to caching
        assert mock_client.post.call_count == 1


class TestAnalytics:
    """Test analytics functionality."""

    @pytest.mark.asyncio
    async def test_model_analytics_generation(self, mock_orchestrator):
        """Test model analytics generation."""
        # Set up some test metrics
        mock_orchestrator.model_metrics = {
            "test-model": ModelPerformanceMetrics("test-model")
        }
        mock_orchestrator.model_metrics["test-model"].add_performance_data(1.0, 0.8, True)

        analytics = await mock_orchestrator.get_model_analytics()

        assert "models" in analytics
        assert "summary" in analytics
        assert "test-model" in analytics["models"]
        assert analytics["summary"]["total_models"] == 1

    @pytest.mark.asyncio
    async def test_optimization_suggestions(self, mock_orchestrator):
        """Test optimization suggestions generation."""
        # Set up metrics with performance issues
        mock_orchestrator.model_metrics = {
            "slow-model": ModelPerformanceMetrics("slow-model")
        }

        metrics = mock_orchestrator.model_metrics["slow-model"]
        # Add slow performance data
        for _ in range(10):
            metrics.add_performance_data(35.0, 0.3, False)  # Slow and low quality

        optimizations = await mock_orchestrator.optimize_model_routing()

        assert "suggestions" in optimizations
        assert len(optimizations["suggestions"]) > 0

        # Should have warnings for slow response and low success rate
        suggestion_types = [s["type"] for s in optimizations["suggestions"]]
        assert "performance_warning" in suggestion_types
        assert "reliability_warning" in suggestion_types


if __name__ == "__main__":
    pytest.main([__file__])
