"""
Comprehensive test suite for the Dependency Engine.
Tests Phase 1 improvements including performance monitoring, caching, and AI integration.
"""

import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone, timedelta

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from ai_coding_agent.services.dependency_engine import DependencyEng<PERSON>, DependencyCache
    from ai_coding_agent.models import (
        TaskStatus, DependencyType, DependencyCheckStatus,
        DependencyCheckResult, BlockingDependency
    )
    from sqlalchemy.orm import Session
except ImportError:
    # Fallback for testing without full module structure
    DependencyEngine = Mock
    DependencyCache = Mock
    TaskStatus = Mock
    DependencyType = Mock
    DependencyCheckStatus = Mock
    DependencyCheckResult = Mock
    BlockingDependency = Mock
    Session = Mock


class TestDependencyCache:
    """Test the enhanced dependency cache with TTL support."""

    def test_cache_initialization(self):
        """Test cache initializes with correct default values."""
        cache = DependencyCache(default_ttl=600)
        assert cache.default_ttl == 600
        assert len(cache.dependency_graph_cache) == 0
        assert len(cache.check_result_cache) == 0

    def test_cache_ttl_expiration(self):
        """Test cache entries expire after TTL."""
        cache = DependencyCache(default_ttl=1)  # 1 second TTL

        # Create mock dependency graph
        mock_graph = Mock()
        cache.set_dependency_graph("test_key", mock_graph)

        # Should be available immediately
        result = cache.get_dependency_graph("test_key")
        assert result is not None
        assert cache.cache_hits['dependency_graph'] == 1

        # Wait for expiration
        import time
        time.sleep(1.1)

        # Should be expired now
        result = cache.get_dependency_graph("test_key")
        assert result is None
        assert cache.cache_evictions['dependency_graph'] == 1

    def test_cache_stats(self):
        """Test cache statistics tracking."""
        cache = DependencyCache()

        # Generate some cache activity
        mock_graph = Mock()
        cache.set_dependency_graph("key1", mock_graph)
        cache.get_dependency_graph("key1")  # Hit
        cache.get_dependency_graph("key2")  # Miss

        stats = cache.get_cache_stats()
        assert stats['hits']['dependency_graph'] == 1
        assert stats['misses']['dependency_graph'] == 1
        assert stats['hit_rate']['dependency_graph'] == 0.5

    def test_cache_clear(self):
        """Test cache clearing functionality."""
        cache = DependencyCache()

        mock_graph = Mock()
        cache.set_dependency_graph("test_key", mock_graph)
        assert len(cache.dependency_graph_cache) == 1

        cache.clear_cache()
        assert len(cache.dependency_graph_cache) == 0


class TestDependencyEnginePerformance:
    """Test performance monitoring and optimization features."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock(spec=Session)
        session.execute = Mock()
        return session

    @pytest.fixture
    def dependency_engine(self, mock_db_session):
        """Create a dependency engine instance for testing."""
        return DependencyEngine(mock_db_session, user_id="test_user")

    def test_performance_monitoring_decorator(self, dependency_engine):
        """Test that performance monitoring decorator works."""
        with patch('src.ai_coding_agent.services.dependency_engine.logger') as mock_logger:
            # Mock the database query with proper structure
            mock_phase = Mock()
            mock_phase.roadmap_id = "roadmap_1"
            mock_phase.dependencies = []  # Empty dependencies list

            mock_step = Mock()
            mock_step.phase = mock_phase
            mock_step.dependencies = []  # Empty dependencies list

            mock_task = Mock()
            mock_task.id = "task_1"
            mock_task.name = "Test Task"
            mock_task.status = TaskStatus.PENDING if hasattr(TaskStatus, 'PENDING') else "pending"
            mock_task.dependencies = []
            mock_task.step = mock_step

            # Mock the database queries
            dependency_engine.db.execute.return_value.scalar_one_or_none.return_value = mock_task
            dependency_engine.db.execute.return_value.scalars.return_value.all.return_value = [mock_task]

            # Call monitored method
            result = dependency_engine.can_start_task("task_1")

            # Verify performance logging occurred
            mock_logger.info.assert_called()
            log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
            performance_logs = [log for log in log_calls if "check_task_dependencies completed" in log]
            assert len(performance_logs) > 0

    def test_database_configuration(self, dependency_engine):
        """Test database session configuration."""
        # Verify that database configuration was attempted
        dependency_engine.db.execute.assert_called()

    def test_cache_integration(self, dependency_engine):
        """Test cache integration with dependency checks."""
        # Test that cache is properly initialized
        assert dependency_engine.cache is not None
        assert isinstance(dependency_engine.cache, DependencyCache)

        # Test cache statistics are available
        stats = dependency_engine.cache.get_cache_stats()
        assert 'hits' in stats
        assert 'misses' in stats
        assert 'hit_rate' in stats


class TestAIIntegration:
    """Test AI integration improvements."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock(spec=Session)
        session.execute = Mock()
        return session

    @pytest.fixture
    def dependency_engine(self, mock_db_session):
        """Create a dependency engine instance for testing."""
        return DependencyEngine(mock_db_session, user_id="test_user")

    @pytest.mark.asyncio
    async def test_ai_dependency_prediction_success(self, dependency_engine):
        """Test successful AI dependency prediction."""
        # Mock AI orchestrator
        mock_orchestrator = AsyncMock()
        mock_response = Mock()
        mock_response.result = '["database_connection", "api_authentication", "file_upload_service"]'
        mock_orchestrator.execute_task.return_value = mock_response

        # Mock the AI orchestrator initialization
        with patch.object(dependency_engine, '_get_ai_orchestrator', return_value=mock_orchestrator):
            dependency_engine._task_request_class = Mock()

            result = await dependency_engine.predict_dependencies_with_ai(
                "Create user authentication system",
                {"project_type": "web_app", "technology": "python"}
            )

            assert isinstance(result, list)
            assert len(result) > 0

    @pytest.mark.asyncio
    async def test_ai_dependency_prediction_no_orchestrator(self, dependency_engine):
        """Test AI dependency prediction when orchestrator is not available."""
        # Mock no AI orchestrator available
        with patch.object(dependency_engine, '_get_ai_orchestrator', return_value=None):
            result = await dependency_engine.predict_dependencies_with_ai(
                "Create user authentication system",
                {"project_type": "web_app"}
            )

            assert result == []

    @pytest.mark.asyncio
    async def test_ai_dependency_prediction_error_handling(self, dependency_engine):
        """Test AI dependency prediction error handling."""
        # Mock AI orchestrator that raises an exception
        mock_orchestrator = AsyncMock()
        mock_orchestrator.execute_task.side_effect = Exception("AI service error")

        with patch.object(dependency_engine, '_get_ai_orchestrator', return_value=mock_orchestrator):
            dependency_engine._task_request_class = Mock()

            result = await dependency_engine.predict_dependencies_with_ai(
                "Create user authentication system",
                {"project_type": "web_app"}
            )

            assert result == []


class TestDependencyEngineIntegration:
    """Integration tests for the dependency engine."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock(spec=Session)
        session.execute = Mock()
        return session

    @pytest.fixture
    def dependency_engine(self, mock_db_session):
        """Create a dependency engine instance for testing."""
        return DependencyEngine(mock_db_session, user_id="test_user")

    def test_dependency_check_with_caching(self, dependency_engine):
        """Test dependency checking with caching enabled."""
        # Mock task data with proper structure
        mock_phase = Mock()
        mock_phase.roadmap_id = "roadmap_1"
        mock_phase.dependencies = []  # Empty dependencies list

        mock_step = Mock()
        mock_step.phase = mock_phase
        mock_step.dependencies = []  # Empty dependencies list

        mock_task = Mock()
        mock_task.id = "task_1"
        mock_task.name = "Test Task"
        mock_task.status = TaskStatus.PENDING if hasattr(TaskStatus, 'PENDING') else "pending"
        mock_task.dependencies = []
        mock_task.step = mock_step

        # Mock the database queries
        dependency_engine.db.execute.return_value.scalar_one_or_none.return_value = mock_task
        dependency_engine.db.execute.return_value.scalars.return_value.all.return_value = [mock_task]

        # First call should hit database
        result1 = dependency_engine.can_start_task("task_1")
        assert result1.status == DependencyCheckStatus.SATISFIED

        # Verify cache statistics
        stats = dependency_engine.cache.get_cache_stats()
        assert 'check_result' in stats['misses'] or 'check_result' in stats['hits']

    def test_performance_metrics_collection(self, dependency_engine):
        """Test that performance metrics are collected."""
        # Verify performance metrics structure exists
        assert hasattr(dependency_engine, 'performance_metrics')
        assert isinstance(dependency_engine.performance_metrics, dict)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
