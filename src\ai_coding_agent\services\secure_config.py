"""
Secure configuration management for API keys and sensitive data.

This module provides encryption/decryption for sensitive configuration
data like API keys, ensuring they are never stored in plain text.
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from ..config import settings


class SecureConfigManager:
    """
    Manages secure storage and retrieval of sensitive configuration data.
    
    Features:
    - Encrypts API keys and sensitive data
    - Uses environment-based encryption key
    - Provides secure read/write operations
    - Maintains backward compatibility
    """
    
    def __init__(self):
        """Initialize the secure config manager."""
        self.config_path = Path(__file__).parent.parent / "models_config.json"
        self.secure_config_path = Path(__file__).parent.parent / "secure_config.enc"
        self._fernet = self._get_encryption_key()
    
    def _get_encryption_key(self) -> Ferne<PERSON>:
        """
        Generate or retrieve the encryption key.
        
        Returns:
            Fernet: Encryption/decryption instance
        """
        # Get encryption password from environment
        password = os.getenv("CONFIG_ENCRYPTION_KEY", settings.security.secret_key)
        
        # Generate a salt (in production, this should be stored securely)
        salt = b"ai_coding_agent_salt"  # In production, use a random salt per installation
        
        # Derive key from password
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return Fernet(key)
    
    def encrypt_api_key(self, api_key: str) -> str:
        """
        Encrypt an API key.
        
        Args:
            api_key: The plain text API key
            
        Returns:
            str: Encrypted API key (base64 encoded)
        """
        if not api_key:
            return ""
        
        encrypted = self._fernet.encrypt(api_key.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt_api_key(self, encrypted_key: str) -> str:
        """
        Decrypt an API key.
        
        Args:
            encrypted_key: The encrypted API key
            
        Returns:
            str: Decrypted API key
        """
        if not encrypted_key:
            return ""
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_key.encode())
            decrypted = self._fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception:
            # If decryption fails, assume it's a plain text key (backward compatibility)
            return encrypted_key
    
    def save_provider_config(self, provider_name: str, api_key: str, 
                           base_url: Optional[str] = None, enabled: bool = True) -> bool:
        """
        Securely save a cloud provider configuration.
        
        Args:
            provider_name: Name of the provider (openai, anthropic, etc.)
            api_key: The API key to encrypt and store
            base_url: Optional custom base URL
            enabled: Whether the provider is enabled
            
        Returns:
            bool: True if saved successfully
        """
        try:
            # Load existing configuration
            config = self.load_config()
            
            # Ensure providers section exists
            if "providers" not in config:
                config["providers"] = {}
            
            # Encrypt the API key
            encrypted_key = self.encrypt_api_key(api_key)
            
            # Store provider configuration
            config["providers"][provider_name] = {
                "type": "cloud",
                "api_key_encrypted": encrypted_key,
                "base_url": base_url,
                "enabled": enabled,
                "encryption_version": "v1"  # For future migration support
            }
            
            # Save configuration
            return self.save_config(config)
            
        except Exception as e:
            print(f"Error saving provider config: {e}")
            return False
    
    def get_provider_api_key(self, provider_name: str) -> Optional[str]:
        """
        Retrieve and decrypt a provider's API key.
        
        Args:
            provider_name: Name of the provider
            
        Returns:
            str: Decrypted API key, or None if not found
        """
        try:
            config = self.load_config()
            provider_config = config.get("providers", {}).get(provider_name, {})
            
            # Check for encrypted key first
            if "api_key_encrypted" in provider_config:
                return self.decrypt_api_key(provider_config["api_key_encrypted"])
            
            # Fallback to plain text key (backward compatibility)
            return provider_config.get("api_key")
            
        except Exception as e:
            print(f"Error retrieving API key for {provider_name}: {e}")
            return None
    
    def load_config(self) -> Dict[str, Any]:
        """
        Load the configuration file.
        
        Returns:
            dict: Configuration data
        """
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
        except Exception as e:
            print(f"Error loading config: {e}")
            return {}
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        Save the configuration file.
        
        Args:
            config: Configuration data to save
            
        Returns:
            bool: True if saved successfully
        """
        try:
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def migrate_plain_text_keys(self) -> bool:
        """
        Migrate existing plain text API keys to encrypted format.
        
        Returns:
            bool: True if migration was successful
        """
        try:
            config = self.load_config()
            providers = config.get("providers", {})
            
            migrated = False
            for provider_name, provider_config in providers.items():
                # Check if this provider has a plain text API key
                if "api_key" in provider_config and "api_key_encrypted" not in provider_config:
                    api_key = provider_config["api_key"]
                    if api_key:
                        # Encrypt the key
                        encrypted_key = self.encrypt_api_key(api_key)
                        
                        # Update configuration
                        provider_config["api_key_encrypted"] = encrypted_key
                        provider_config["encryption_version"] = "v1"
                        
                        # Remove plain text key
                        del provider_config["api_key"]
                        
                        migrated = True
            
            if migrated:
                return self.save_config(config)
            
            return True
            
        except Exception as e:
            print(f"Error migrating API keys: {e}")
            return False


# Global instance
secure_config = SecureConfigManager()
