"""
Test Supabase Authentication System.

This test script validates the Supabase Auth integration with local user management
and ensures all authentication endpoints are working correctly.
"""

import asyncio
import json
from typing import Dict, Any
import httpx
import pytest
import os
from datetime import datetime

from ai_coding_agent.config import settings
from ai_coding_agent.services.supabase_auth import SupabaseAuthService
from ai_coding_agent.models.user import UserCreate, UserLogin
from ai_coding_agent.main import app


class TestSupabaseAuth:
    """Test Supabase authentication functionality."""

    def __init__(self):
        self.base_url = f"http://{settings.host}:{settings.port}"
        self.auth_service = SupabaseAuthService()
        self.test_user_data = {
            "email": f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
            "password": "TestPassword123!",
            "username": f"testuser_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "full_name": "Test User"
        }

    async def test_supabase_connection(self):
        """Test basic Supabase connection."""
        print("🔍 Testing Supabase connection...")

        try:
            # Test if Supabase service can be initialized
            service = SupabaseAuthService()

            # Check if we have Supabase credentials
            if not settings.supabase.url or not settings.supabase.anon_key:
                print("⚠️  Supabase credentials not configured")
                print(f"   SUPABASE_URL: {'✓ Set' if settings.supabase.url else '✗ Missing'}")
                print(f"   SUPABASE_ANON_KEY: {'✓ Set' if settings.supabase.anon_key else '✗ Missing'}")
                return False

            print("✅ Supabase service initialized successfully")
            print(f"   URL: {settings.supabase.url}")
            print(f"   Key: {'*' * 20}...")
            return True

        except Exception as e:
            print(f"❌ Supabase connection failed: {e}")
            return False

    async def test_registration_endpoint(self):
        """Test user registration endpoint."""
        print("\n🔍 Testing user registration endpoint...")

        try:
            async with httpx.AsyncClient(base_url=self.base_url) as client:
                response = await client.post(
                    "/api/v1/supabase-auth/register",
                    json=self.test_user_data
                )

                print(f"   Status: {response.status_code}")

                if response.status_code == 201:
                    data = response.json()
                    print("✅ Registration successful")
                    print(f"   User ID: {data.get('user', {}).get('id', 'N/A')}")
                    print(f"   Email: {data.get('user', {}).get('email', 'N/A')}")
                    print(f"   Confirmation required: {data.get('confirmation_required', False)}")
                    return data
                else:
                    print(f"❌ Registration failed: {response.text}")
                    return None

        except Exception as e:
            print(f"❌ Registration endpoint error: {e}")
            return None

    async def test_login_endpoint(self):
        """Test user login endpoint."""
        print("\n🔍 Testing user login endpoint...")

        try:
            login_data = {
                "email": self.test_user_data["email"],
                "password": self.test_user_data["password"]
            }

            async with httpx.AsyncClient(base_url=self.base_url) as client:
                response = await client.post(
                    "/api/v1/supabase-auth/login",
                    json=login_data
                )

                print(f"   Status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    print("✅ Login successful")
                    print(f"   Access token: {'*' * 10}...")
                    print(f"   Token type: {data.get('token_type', 'N/A')}")
                    print(f"   Expires in: {data.get('expires_in', 'N/A')} seconds")
                    return data
                else:
                    print(f"❌ Login failed: {response.text}")
                    return None

        except Exception as e:
            print(f"❌ Login endpoint error: {e}")
            return None

    async def test_protected_endpoint(self, access_token: str):
        """Test accessing a protected endpoint with token."""
        print("\n🔍 Testing protected endpoint access...")

        try:
            headers = {"Authorization": f"Bearer {access_token}"}

            async with httpx.AsyncClient(app=app, base_url=self.base_url) as client:
                response = await client.get(
                    "/api/v1/supabase-auth/profile",
                    headers=headers
                )

                print(f"   Status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    print("✅ Protected endpoint access successful")
                    print(f"   User profile: {json.dumps(data, indent=2)}")
                    return data
                else:
                    print(f"❌ Protected endpoint access failed: {response.text}")
                    return None

        except Exception as e:
            print(f"❌ Protected endpoint error: {e}")
            return None

    async def test_logout_endpoint(self, access_token: str):
        """Test user logout endpoint."""
        print("\n🔍 Testing user logout endpoint...")

        try:
            headers = {"Authorization": f"Bearer {access_token}"}

            async with httpx.AsyncClient(app=app, base_url=self.base_url) as client:
                response = await client.post(
                    "/api/v1/supabase-auth/logout",
                    headers=headers
                )

                print(f"   Status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    print("✅ Logout successful")
                    print(f"   Message: {data.get('message', 'N/A')}")
                    return True
                else:
                    print(f"❌ Logout failed: {response.text}")
                    return False

        except Exception as e:
            print(f"❌ Logout endpoint error: {e}")
            return False

    async def test_password_reset_endpoint(self):
        """Test password reset endpoint."""
        print("\n🔍 Testing password reset endpoint...")

        try:
            reset_data = {"email": self.test_user_data["email"]}

            async with httpx.AsyncClient(app=app, base_url=self.base_url) as client:
                response = await client.post(
                    "/api/v1/supabase-auth/reset-password",
                    json=reset_data
                )

                print(f"   Status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    print("✅ Password reset request successful")
                    print(f"   Message: {data.get('message', 'N/A')}")
                    return True
                else:
                    print(f"❌ Password reset failed: {response.text}")
                    return False

        except Exception as e:
            print(f"❌ Password reset endpoint error: {e}")
            return False

    async def test_auth_service_directly(self):
        """Test the authentication service directly."""
        print("\n🔍 Testing authentication service directly...")

        try:
            # Test user creation
            user_create = UserCreate(**self.test_user_data)
            result = await self.auth_service.register_user(
                email=user_create.email,
                password=user_create.password,
                username=user_create.username,
                full_name=user_create.full_name
            )

            if result:
                print("✅ Direct service registration successful")
                print(f"   User ID: {result.get('user', {}).get('id', 'N/A')}")

                # Test login
                login_result = await self.auth_service.login_user(
                    email=user_create.email,
                    password=user_create.password
                )

                if login_result:
                    print("✅ Direct service login successful")
                    print(f"   Access token available: {'access_token' in login_result}")
                    return login_result
                else:
                    print("❌ Direct service login failed")
                    return None
            else:
                print("❌ Direct service registration failed")
                return None

        except Exception as e:
            print(f"❌ Direct service test error: {e}")
            return None

    async def run_comprehensive_test(self):
        """Run all authentication tests."""
        print("🚀 Starting Supabase Authentication Test Suite")
        print("=" * 60)

        results = {}

        # Test 1: Basic connection
        results['connection'] = await self.test_supabase_connection()

        if not results['connection']:
            print("\n❌ Cannot proceed without Supabase connection")
            return results

        # Test 2: Direct service test
        results['direct_service'] = await self.test_auth_service_directly()

        # Test 3: API endpoints
        registration_result = await self.test_registration_endpoint()
        results['registration'] = registration_result is not None

        if registration_result:
            # Test login
            login_result = await self.test_login_endpoint()
            results['login'] = login_result is not None

            if login_result and 'access_token' in login_result:
                access_token = login_result['access_token']

                # Test protected endpoint
                results['protected_access'] = await self.test_protected_endpoint(access_token)

                # Test logout
                results['logout'] = await self.test_logout_endpoint(access_token)

        # Test password reset (doesn't require registration)
        results['password_reset'] = await self.test_password_reset_endpoint()

        # Print summary
        print("\n" + "=" * 60)
        print("📋 Test Results Summary:")
        print("=" * 60)

        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")

        passed = sum(1 for r in results.values() if r)
        total = len(results)

        print(f"\n📊 Overall: {passed}/{total} tests passed")

        if passed == total:
            print("🎉 All tests passed! Supabase Auth is working correctly.")
        elif passed > 0:
            print("⚠️  Some tests passed. Check configuration or credentials.")
        else:
            print("❌ All tests failed. Check Supabase setup and configuration.")

        return results


async def main():
    """Main test function."""
    tester = TestSupabaseAuth()
    await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
