#!/usr/bin/env python3
"""
Detailed PostgreSQL test for AI Coding Agent
"""

import os
import sys
import uuid
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_coding_agent.config import settings
from ai_coding_agent.models.base import get_hybrid_db_manager
from sqlalchemy import text

def test_postgresql():
    """Test PostgreSQL connection and operations"""
    print("🐘 Testing PostgreSQL Database")
    print("=" * 50)

    try:
        # Load settings
        print(f"🔧 Database mode: {settings.hybrid_db.mode}")
        print(f"🔧 PostgreSQL URL: {settings.database.url}")

        # Initialize manager
        db_manager = get_hybrid_db_manager()
        print("✅ Database manager initialized")

        # Test PostgreSQL connection
        print("\n🧪 Testing PostgreSQL connection...")
        with db_manager.local_engine.connect() as conn:
            result = conn.execute(text('SELECT version()'))
            version = result.fetchone()[0]
            print(f"✅ PostgreSQL connected: {version[:80]}...")

            # Test table existence
            result = conn.execute(text("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            print(f"📋 Tables found ({len(tables)}): {', '.join(tables)}")

            # Test CRUD operations
            print("\n🧪 Testing CRUD operations...")

            # Insert test project
            project_id = str(uuid.uuid4())
            print(f"📝 Inserting test project with ID: {project_id}")

            conn.execute(text("""
                INSERT INTO projects (id, name, description, tech_stack, project_rules, created_at, updated_at)
                VALUES (:id, :name, :description, :tech_stack, :project_rules, :created_at, :updated_at)
            """), {
                'id': project_id,
                'name': 'Test PostgreSQL Project',
                'description': 'Testing PostgreSQL functionality',
                'tech_stack': '{}',
                'project_rules': '{}',
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            conn.commit()
            print("✅ Project inserted successfully")

            # Read test project
            result = conn.execute(text("""
                SELECT name, description, created_at FROM projects WHERE id = :id
            """), {'id': project_id})
            project = result.fetchone()
            if project:
                print(f"✅ Project retrieved: '{project[0]}' - {project[1]}")
                print(f"   Created at: {project[2]}")
            else:
                print("❌ Project not found after insert")

            # Update test project
            new_description = "Updated PostgreSQL test project"
            conn.execute(text("""
                UPDATE projects SET description = :description, updated_at = :updated_at
                WHERE id = :id
            """), {
                'id': project_id,
                'description': new_description,
                'updated_at': datetime.utcnow()
            })
            conn.commit()
            print("✅ Project updated successfully")

            # Verify update
            result = conn.execute(text("""
                SELECT description FROM projects WHERE id = :id
            """), {'id': project_id})
            updated_project = result.fetchone()
            if updated_project and updated_project[0] == new_description:
                print(f"✅ Update verified: {updated_project[0]}")
            else:
                print("❌ Update verification failed")

            # Test roadmap creation (foreign key relationship)
            roadmap_id = str(uuid.uuid4())
            print(f"📝 Creating test roadmap with ID: {roadmap_id}")

            conn.execute(text("""
                INSERT INTO roadmaps (id, project_id, name, version, status, project_metadata, created_at, updated_at)
                VALUES (:id, :project_id, :name, :version, :status, :project_metadata, :created_at, :updated_at)
            """), {
                'id': roadmap_id,
                'project_id': project_id,
                'name': 'Test Roadmap',
                'version': '1.0.0',
                'status': 'planning',
                'project_metadata': '{}',
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            })
            conn.commit()
            print("✅ Roadmap created successfully")

            # Test foreign key relationship
            result = conn.execute(text("""
                SELECT p.name, r.name
                FROM projects p
                JOIN roadmaps r ON p.id = r.project_id
                WHERE p.id = :project_id
            """), {'project_id': project_id})
            relation = result.fetchone()
            if relation:
                print(f"✅ Foreign key relationship working: {relation[0]} -> {relation[1]}")
            else:
                print("❌ Foreign key relationship failed")

            # Count total records
            result = conn.execute(text("SELECT COUNT(*) FROM projects"))
            project_count = result.fetchone()[0]
            result = conn.execute(text("SELECT COUNT(*) FROM roadmaps"))
            roadmap_count = result.fetchone()[0]
            print(f"📊 Total records - Projects: {project_count}, Roadmaps: {roadmap_count}")

            # Clean up test data
            print("\n🧹 Cleaning up test data...")
            conn.execute(text("""
                DELETE FROM roadmaps WHERE id = :id
            """), {'id': roadmap_id})
            conn.execute(text("""
                DELETE FROM projects WHERE id = :id
            """), {'id': project_id})
            conn.commit()
            print("✅ Test data cleaned up successfully")

        print("\n🎉 PostgreSQL test completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ PostgreSQL test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_postgresql()
    sys.exit(0 if success else 1)
