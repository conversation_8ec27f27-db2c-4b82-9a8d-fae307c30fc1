"""
Test authentication endpoints and functionality.

This module tests the authentication system including registration,
login, token management, and authorization.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from ai_coding_agent.models import User
from ai_coding_agent.services.auth import get_password_hash


def test_register_user(client: TestClient):
    """Test user registration endpoint."""
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "testpassword123"
    }

    response = client.post("/api/v1/auth/register", json=user_data)

    assert response.status_code == 201
    data = response.json()

    assert data["email"] == user_data["email"]
    assert data["username"] == user_data["username"]
    assert data["full_name"] == user_data["full_name"]
    assert data["is_active"] is True
    assert "id" in data
    assert "created_at" in data
    # Password should not be returned
    assert "password" not in data
    assert "hashed_password" not in data


def test_register_duplicate_email(client: TestClient, test_db: Session):
    """Test registration with duplicate email fails."""
    # Create first user
    user_data = {
        "email": "<EMAIL>",
        "username": "user1",
        "password": "password123"
    }
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201

    # Try to create second user with same email
    user_data2 = {
        "email": "<EMAIL>",
        "username": "user2",
        "password": "password123"
    }
    response = client.post("/api/v1/auth/register", json=user_data2)
    assert response.status_code == 400
    assert "Email already registered" in response.json()["detail"]


def test_register_duplicate_username(client: TestClient):
    """Test registration with duplicate username fails."""
    # Create first user
    user_data = {
        "email": "<EMAIL>",
        "username": "duplicateuser",
        "password": "password123"
    }
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201

    # Try to create second user with same username
    user_data2 = {
        "email": "<EMAIL>",
        "username": "duplicateuser",
        "password": "password123"
    }
    response = client.post("/api/v1/auth/register", json=user_data2)
    assert response.status_code == 400
    assert "Username already registered" in response.json()["detail"]


def test_login_success(client: TestClient):
    """Test successful user login."""
    # Register user first
    user_data = {
        "email": "<EMAIL>",
        "username": "loginuser",
        "password": "loginpassword123"
    }
    register_response = client.post("/api/v1/auth/register", json=user_data)
    assert register_response.status_code == 201

    # Login with username
    login_data = {
        "username": "loginuser",
        "password": "loginpassword123"
    }
    response = client.post("/api/v1/auth/login", json=login_data)

    assert response.status_code == 200
    data = response.json()

    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"
    assert "expires_in" in data
    assert "user" in data

    # Verify user data
    user = data["user"]
    assert user["email"] == user_data["email"]
    assert user["username"] == user_data["username"]


def test_login_with_email(client: TestClient):
    """Test login using email instead of username."""
    # Register user first
    user_data = {
        "email": "<EMAIL>",
        "username": "emailuser",
        "password": "emailpassword123"
    }
    register_response = client.post("/api/v1/auth/register", json=user_data)
    assert register_response.status_code == 201

    # Login with email
    login_data = {
        "username": "<EMAIL>",  # Using email as username
        "password": "emailpassword123"
    }
    response = client.post("/api/v1/auth/login", json=login_data)

    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data


def test_login_invalid_credentials(client: TestClient):
    """Test login with invalid credentials."""
    login_data = {
        "username": "nonexistent",
        "password": "wrongpassword"
    }
    response = client.post("/api/v1/auth/login", json=login_data)

    assert response.status_code == 401
    assert "Incorrect username or password" in response.json()["detail"]


def test_get_current_user(client: TestClient):
    """Test getting current user information."""
    # Register and login user
    user_data = {
        "email": "<EMAIL>",
        "username": "currentuser",
        "password": "currentpassword123"
    }
    client.post("/api/v1/auth/register", json=user_data)

    login_response = client.post("/api/v1/auth/login", json={
        "username": "currentuser",
        "password": "currentpassword123"
    })

    token = login_response.json()["access_token"]

    # Get current user info
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/auth/me", headers=headers)

    assert response.status_code == 200
    data = response.json()

    assert data["email"] == user_data["email"]
    assert data["username"] == user_data["username"]


def test_get_current_user_invalid_token(client: TestClient):
    """Test getting current user with invalid token."""
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/v1/auth/me", headers=headers)

    assert response.status_code == 401
    assert "Could not validate credentials" in response.json()["detail"]


def test_logout(client: TestClient):
    """Test user logout."""
    # Register and login user
    user_data = {
        "email": "<EMAIL>",
        "username": "logoutuser",
        "password": "logoutpassword123"
    }
    client.post("/api/v1/auth/register", json=user_data)

    login_response = client.post("/api/v1/auth/login", json={
        "username": "logoutuser",
        "password": "logoutpassword123"
    })

    token = login_response.json()["access_token"]

    # Logout
    headers = {"Authorization": f"Bearer {token}"}
    response = client.post("/api/v1/auth/logout", headers=headers)

    assert response.status_code == 200
    assert "logged out successfully" in response.json()["message"]


def test_refresh_token(client: TestClient):
    """Test token refresh functionality."""
    # Register and login user
    user_data = {
        "email": "<EMAIL>",
        "username": "refreshuser",
        "password": "refreshpassword123"
    }
    client.post("/api/v1/auth/register", json=user_data)

    login_response = client.post("/api/v1/auth/login", json={
        "username": "refreshuser",
        "password": "refreshpassword123"
    })

    refresh_token = login_response.json()["refresh_token"]

    # Refresh token
    response = client.post("/api/v1/auth/refresh", json={"refresh_token": refresh_token})

    assert response.status_code == 200
    data = response.json()

    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert "expires_in" in data
