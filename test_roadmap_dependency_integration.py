"""
Integration Tests for Roadmap Service with Dependency Engine
Tests the integration between roadmap service and dependency engine.
"""

import pytest
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from uuid import uuid4

from src.ai_coding_agent.models import (
    Base, Project, Roadmap, Phase, Step, Task,
    TaskStatus, AgentType,
    ProjectCreate, RoadmapCreate, PhaseCreate, StepCreate, TaskCreate
)
from src.ai_coding_agent.services.roadmap import RoadmapService


@pytest.fixture
def db_session():
    """Create a test database session."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()


@pytest.fixture
def roadmap_service(db_session):
    """Create a roadmap service instance."""
    return RoadmapService(db_session, user_id="test_user", user_email="<EMAIL>")


@pytest.fixture
def sample_project_data():
    """Create sample project data for testing."""
    return ProjectCreate(
        name="Test Project",
        description="Test project for dependency integration",
        roadmap=RoadmapCreate(
            name="Test Roadmap",
            version="1.0.0",
            phases=[
                PhaseCreate(
                    name="Phase 1",
                    description="First phase",
                    order_index=1,
                    dependencies=[],
                    steps=[
                        StepCreate(
                            name="Step 1.1",
                            description="First step",
                            order_index=1,
                            dependencies=[],
                            tasks=[
                                TaskCreate(
                                    name="Task 1.1.1",
                                    description="First task",
                                    order_index=1,
                                    assigned_agent=AgentType.BACKEND,
                                    dependencies=[]
                                ),
                                TaskCreate(
                                    name="Task 1.1.2",
                                    description="Second task",
                                    order_index=2,
                                    assigned_agent=AgentType.FRONTEND,
                                    dependencies=[]  # Will be set after creation
                                )
                            ]
                        ),
                        StepCreate(
                            name="Step 1.2",
                            description="Second step",
                            order_index=2,
                            dependencies=[],  # Will be set after creation
                            tasks=[
                                TaskCreate(
                                    name="Task 1.2.1",
                                    description="Third task",
                                    order_index=1,
                                    assigned_agent=AgentType.BACKEND,
                                    dependencies=[]
                                )
                            ]
                        )
                    ]
                ),
                PhaseCreate(
                    name="Phase 2",
                    description="Second phase",
                    order_index=2,
                    dependencies=[],  # Will be set after creation
                    steps=[
                        StepCreate(
                            name="Step 2.1",
                            description="Phase 2 step",
                            order_index=1,
                            dependencies=[],
                            tasks=[
                                TaskCreate(
                                    name="Task 2.1.1",
                                    description="Phase 2 task",
                                    order_index=1,
                                    assigned_agent=AgentType.FRONTEND,
                                    dependencies=[]
                                )
                            ]
                        )
                    ]
                )
            ]
        )
    )


class TestRoadmapDependencyIntegration:
    """Integration tests for roadmap service with dependency engine."""

    def test_create_project_with_dependencies(self, roadmap_service, sample_project_data):
        """Test creating a project and setting up dependencies."""
        project = roadmap_service.create_project(sample_project_data)
        
        assert project.name == "Test Project"
        assert project.roadmap is not None
        assert len(project.roadmap.phases) == 2
        
        # Verify dependency engine is initialized
        assert roadmap_service.dependency_engine is not None

    def test_task_dependency_checking(self, roadmap_service, sample_project_data):
        """Test task dependency checking through roadmap service."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get the first task
        first_task = project.roadmap.phases[0].steps[0].tasks[0]
        
        # Check dependencies
        dep_result = roadmap_service.check_task_dependencies(first_task.id)
        
        assert dep_result["can_start"] is True
        assert dep_result["status"] == "can_start"
        assert len(dep_result["blocking_dependencies"]) == 0

    def test_start_task_with_validation(self, roadmap_service, sample_project_data):
        """Test starting a task with dependency validation."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get the first task (no dependencies)
        first_task = project.roadmap.phases[0].steps[0].tasks[0]
        
        # Start the task
        updated_task = roadmap_service.start_task(first_task.id)
        
        assert updated_task.status == TaskStatus.IN_PROGRESS

    def test_complete_task_with_progression(self, roadmap_service, sample_project_data):
        """Test completing a task and automatic progression."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get tasks from first step
        first_step = project.roadmap.phases[0].steps[0]
        task1 = first_step.tasks[0]
        task2 = first_step.tasks[1]
        
        # Complete both tasks
        roadmap_service.complete_task(task1.id)
        roadmap_service.complete_task(task2.id)
        
        # Check if step was automatically completed
        step_status = roadmap_service.get_step(first_step.id)
        # Note: This would require implementing get_step method or checking through other means

    def test_phase_progression_checking(self, roadmap_service, sample_project_data):
        """Test phase progression checking."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get the first phase
        first_phase = project.roadmap.phases[0]
        
        # Check progression status
        progression = roadmap_service.get_phase_progression_status(first_phase.id)
        
        assert progression["phase_id"] == first_phase.id
        assert progression["can_progress"] is False  # No tasks completed yet
        assert progression["completion_percentage"] == 0.0

    def test_execution_order_validation(self, roadmap_service, sample_project_data):
        """Test execution order validation."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get a task
        first_task = project.roadmap.phases[0].steps[0].tasks[0]
        
        # Validate execution order
        validation = roadmap_service.validate_execution_order(first_task.id, "task")
        
        assert validation["is_valid"] is True
        assert validation["entity_type"] == "task"

    def test_dependency_override_workflow(self, roadmap_service, sample_project_data):
        """Test the complete dependency override workflow."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Manually set up a dependency to test override
        phase1 = project.roadmap.phases[0]
        step1 = phase1.steps[0]
        task1 = step1.tasks[0]
        task2 = step1.tasks[1]
        
        # Make task2 depend on task1
        task2.dependencies = [task1.id]
        roadmap_service.db.commit()
        
        # Try to start task2 without completing task1 (should fail)
        try:
            roadmap_service.start_task(task2.id)
            assert False, "Should have failed due to dependencies"
        except Exception as e:
            assert "Cannot start task" in str(e)
        
        # Try with override
        updated_task = roadmap_service.start_task(
            task2.id, 
            force_override=True, 
            override_reason="Testing override mechanism"
        )
        
        assert updated_task.status == TaskStatus.IN_PROGRESS

    def test_real_time_status_integration(self, roadmap_service, sample_project_data):
        """Test real-time status integration."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get real-time status
        status = roadmap_service.dependency_engine.get_real_time_status_summary(project.roadmap.id)
        
        assert status["roadmap_id"] == project.roadmap.id
        assert "phases" in status
        assert len(status["phases"]) == 2
        assert status["overall_progress"] == 0.0  # No tasks completed yet

    def test_status_propagation_impact(self, roadmap_service, sample_project_data):
        """Test status propagation impact analysis."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get the first task
        first_task = project.roadmap.phases[0].steps[0].tasks[0]
        
        # Analyze impact of completing the task
        impact = roadmap_service.dependency_engine.get_status_propagation_impact(
            first_task.id, 
            roadmap_service.dependency_engine.DependencyType.TASK,
            TaskStatus.COMPLETED.value
        )
        
        assert impact["entity_id"] == first_task.id
        assert impact["entity_type"] == "task"
        assert impact["new_status"] == TaskStatus.COMPLETED.value

    def test_circular_dependency_prevention(self, roadmap_service, sample_project_data):
        """Test circular dependency detection and prevention."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get tasks
        task1 = project.roadmap.phases[0].steps[0].tasks[0]
        task2 = project.roadmap.phases[0].steps[0].tasks[1]
        
        # Set up circular dependency
        task1.dependencies = [task2.id]
        task2.dependencies = [task1.id]
        roadmap_service.db.commit()
        
        # Check for circular dependencies
        warnings = roadmap_service.dependency_engine.check_circular_dependencies(
            task1.id, 
            roadmap_service.dependency_engine.DependencyType.TASK
        )
        
        # Should detect the circular dependency
        assert isinstance(warnings, list)

    def test_bulk_status_updates_with_dependencies(self, roadmap_service, sample_project_data):
        """Test bulk status updates with dependency validation."""
        project = roadmap_service.create_project(sample_project_data)
        
        # Get all tasks
        all_tasks = []
        for phase in project.roadmap.phases:
            for step in phase.steps:
                all_tasks.extend(step.tasks)
        
        # Try to complete all tasks at once (some should fail due to dependencies)
        updates = [
            {"task_id": task.id, "status": TaskStatus.COMPLETED.value}
            for task in all_tasks
        ]
        
        # This would test the bulk update functionality if implemented
        # For now, just verify we have the tasks
        assert len(all_tasks) == 4  # Based on sample data


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
