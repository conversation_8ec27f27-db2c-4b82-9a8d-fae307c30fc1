#!/usr/bin/env python3
"""
Final PostgreSQL Integration Test for AI Coding Agent
Tests all components of the hybrid database system
"""

def test_postgresql_integration():
    """Test complete PostgreSQL integration."""
    print('🚀 Final PostgreSQL Integration Test')
    print('=' * 50)

    try:
        # 1. Test database setup
        print('1️⃣ Testing database setup...')
        from src.ai_coding_agent.config import settings
        print(f'✅ Settings loaded - Mode: {settings.hybrid_db.mode}')

        # 2. Test hybrid manager
        print('2️⃣ Testing hybrid database manager...')
        from src.ai_coding_agent.models.base import get_hybrid_db_manager
        db_manager = get_hybrid_db_manager()
        print(f'✅ Manager initialized - Mode: {db_manager.mode}')

        # 3. Test PostgreSQL connection
        print('3️⃣ Testing PostgreSQL connection...')
        status = db_manager.get_connection_status()
        if status['local_db'] == 'connected':
            print('✅ PostgreSQL connection: OK')
        else:
            print(f'❌ PostgreSQL connection: {status["local_db"]}')

        # 4. Test data model import
        print('4️⃣ Testing data models...')
        try:
            from src.ai_coding_agent.models.roadmap import Project, Roadmap, Phase, Step, Task
            print('✅ All roadmap models imported successfully')
        except Exception as e:
            print(f'❌ Model import failed: {e}')

        # 5. Test service layer
        print('5️⃣ Testing service layer...')
        try:
            from src.ai_coding_agent.services.roadmap import RoadmapService
            # Create a session for testing
            db_session = next(db_manager.get_local_db())
            try:
                service = RoadmapService(db_session)
                print('✅ Roadmap service initialized with database session')
            finally:
                db_session.close()
        except Exception as e:
            print(f'❌ Service initialization failed: {e}')

        # 6. Test Phase B1 functionality
        print('6️⃣ Testing Phase B1: Roadmap System...')
        try:
            # Test creating tables
            db_manager.create_local_tables()
            print('✅ Database tables created successfully')

            # Test basic roadmap operations
            if status['local_db'] == 'connected':
                print('✅ Ready for roadmap operations')
            else:
                print('⚠️ PostgreSQL not connected - using fallback')

        except Exception as e:
            print(f'❌ Phase B1 test failed: {e}')

        print('\n🎉 PostgreSQL Integration Test Complete!')

        # Summary
        if status['local_db'] == 'connected':
            print('✅ All systems are operational and ready for development!')
            print('🚀 Phase B1: Roadmap System is READY!')
        else:
            print('⚠️ PostgreSQL connection issue - check database setup')

        return status['local_db'] == 'connected'

    except Exception as e:
        print(f'❌ Integration test failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_postgresql_integration()
    exit(0 if success else 1)
