"""
Admin authentication middleware for protecting admin endpoints.

This module provides authentication and authorization middleware
specifically for admin routes, ensuring only superusers can access
administrative functions.
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..models import get_db, User
from ..services.auth import get_current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> User:
    """
    Dependency to ensure current user is an admin (superuser).
    
    This function should be used as a dependency for all admin endpoints
    to ensure only authorized users can access administrative functions.
    
    Args:
        current_user: The currently authenticated user
        db: Database session
        
    Returns:
        User: The current user if they are an admin
        
    Raises:
        HTTPException: If user is not authenticated or not an admin
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not getattr(current_user, 'is_active', False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user account"
        )
    
    if not getattr(current_user, 'is_superuser', False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required. Contact your administrator."
        )
    
    return current_user


async def get_current_admin_user_optional(
    current_user: Optional[User] = Depends(get_current_user),
) -> Optional[User]:
    """
    Optional admin dependency that returns None if user is not admin.
    
    Useful for endpoints that have different behavior for admin vs regular users.
    
    Args:
        current_user: The currently authenticated user (optional)
        
    Returns:
        User: The current user if they are an admin, None otherwise
    """
    if not current_user:
        return None
        
    if not getattr(current_user, 'is_active', False):
        return None
        
    if not getattr(current_user, 'is_superuser', False):
        return None
        
    return current_user


def require_admin_permission(permission: str = "admin"):
    """
    Decorator factory for requiring specific admin permissions.
    
    Args:
        permission: The required permission level
        
    Returns:
        Dependency function that checks the permission
    """
    async def check_permission(
        current_admin: User = Depends(get_current_admin_user)
    ) -> User:
        # For now, just check if user is superuser
        # In the future, this could check specific permissions
        if not getattr(current_admin, 'is_superuser', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_admin
    
    return check_permission
